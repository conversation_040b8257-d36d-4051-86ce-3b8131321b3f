import React, { useEffect, useState, useRef } from 'react';
import { DateTime } from 'luxon';

import { COMMON_CLASSNAMES, DANTE_THEME_CHAT } from '@/constants';
import transformLinkUri from '@/helpers/transformLinkUri';
import {
  Menu,
  MenuButton,
  MenuItem,
  MenuItems,
  Transition,
} from '@headlessui/react';

import DButtonIcon from '@/components/Global/DButtonIcon';
import <PERSON><PERSON><PERSON><PERSON><PERSON> from '@/components/Global/DLogo/DShapeLogo';
import DSuggestionPrompt from '@/components/Global/DSuggestionPrompt';
import CloseIcon from '@/components/Global/Icons/CloseIcon';
import ConversationsIcon from '@/components/Global/Icons/ConversationsIcon';
import OptionsIcon from '@/components/Global/Icons/OptionsIcon';
import ReturnIcon from '@/components/Global/Icons/ReturnIcon';
import SendIcon from '@/components/Global/Icons/SendIcon';
import LLMSelector from '../../LLMSelector';
import PoweredByDante from '../../PoweredByDante';
import ChatListMessages from '../ChatListMessages';
import ChevronDownIcon from '@/components/Global/Icons/ChevronDownIcon';
import AiVoiceIcon from '@/components/Global/Icons/AiVoiceIcon';
import MicrophoneIcon from '@/components/Global/Icons/MicrophoneIcon';
import clsx from 'clsx';
import AudioRecorder from '@/components/AudioRecorder';
import DModal from '@/components/Global/DModal';
import DChatPassword from '@/components/DChatPassword';
import DButton from '@/components/Global/DButton';
import AiChatbotIcon from '@/components/Global/Icons/AiChatbotIcon';
import MenuQuickResponse from '../MenuQuickResponse';
import ConfirmIcon from '@/components/Global/Icons/ConfirmIcon';
import MarkAsResovledIcon from '@/components/Global/Icons/MarkAsResovledIcon';
import { useHumanHandoverStore } from '@/stores/humanHandover/humanHandoverStore';
import AttachmentIcon from '@/components/Global/Icons/AttachmentIcon';
import UploadedImagePreview from '@/components/Global/UploadedImagePreview';
import { saveChatbotImages } from '@/services/customization.service';
import DSpinner from '@/components/Global/DSpinner';
import DLoading from '@/components/DLoading';
import DModalEmailTransaction from '@/components/DModalEmailTransaction';

export const Chat = ({
  config,
  chatContainerRef,
  isInApp,
  isPreviewMode,
  showInAppHeader = false,
  isDanteFaq = false,
  handleSendQuestion,
  hiddenConversation = false,
  hiddenPoweredByDante = false,
  handleCloseButton = () => {},
  handleOpenDanteConversations = () => {},
  handleOpenDanteFaq = () => {},
  handleOpenLiveAgent = () => {},
  showCloseBtn = false,
  showMenuBtn = false,
  isAnswerLoading = false,
  showButtons = true,
  dynamicButtons,
  hideFooter = false,
  handleFooterButton,
  showConsent = false,
  setShowConsent = () => {},
  hasMic = false,
  showSuggestionPrompts = false,
  sources,
  openSources,
  setOpenSources,
  showSources,
  sourcesLoading,
  chatImageLoader,
  isInHumanHandoverApp = false,
  humanHandoverConfig = {},
  humanHandoverMarkResolved = () => {},
  humanHandoverTakeConversation = () => {},
  setQuestionIsQuickResponse = () => {},
  interactingWithLiveAgent = false,
  isLiveAgentResolved = false,
  pollResponse = false,
  messagesLoading = false,
  handleShowVoice = () => { },
  isPreviousConversationHasLiveAgent = false,
  showEmailTranscript = false,
  setShowEmailTranscript = () => {},
  currectConversationId = null,
  setNotification = () => {},
}) => {
  const { openedConversation } = useHumanHandoverStore((state) => state);
  const [showConversation, setShowConversation] = useState(!hiddenConversation);
  const [question, setQuestion] = useState('');
  const [isMicrophoneOpen, setIsMicrophoneOpen] = useState(hasMic);
  const [isRecording, setIsRecording] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(!config?.public || false);
  const [uploadedImages, setUploadedImages] = useState([]);
  const fileInputRef = useRef(null);
  const textareaRef = useRef(null);

  useEffect(() => {
    if (!isAnswerLoading && textareaRef.current) {
      textareaRef.current.focus();
    }
  }, [isAnswerLoading]);

  const handleSend = async (tempQuestion = '') => {
    if ((question.trim().length > 0 || tempQuestion.trim().length > 0 || uploadedImages.length > 0)) {
      // Process images if there are any
      let imageUrls = [];
      if (uploadedImages.length > 0 && config?.kb_id) {
        // Create FormData to upload images
        const formData = new FormData();
        uploadedImages.forEach((image, index) => {
          if (typeof image !== 'string') {
            formData.append('file', image);
          }
        });

        try {
          const response = await saveChatbotImages(config.kb_id, formData);
          if (response.data && response.data.url) {
            imageUrls.push(response.data.url);
          }
        } catch (error) {
          console.error('Error uploading images:', error);
        }
      }

      // Send question with image URLs
      handleSendQuestion(tempQuestion || question, imageUrls);
      isDanteFaq && handleOpenDanteFaq();

      setShowConversation(true);
      setQuestion('');
      setUploadedImages([]);
    }
  };

  const handleSuggestionClick = (suggestion) => {
    handleSendQuestion(suggestion.content);
  };

  const handleOpenMicrophone = () => {
    setIsMicrophoneOpen(true);
  };

  const handleRecordingComplete = (audioBlob) => {
    setIsMicrophoneOpen(false);
  };

  const handleImageUpload = (event) => {
    if (event.target.files && event.target.files.length > 0) {
      // Add new images to the array
      const newImages = Array.from(event.target.files);
      setUploadedImages(prev => [...prev, ...newImages]);

      // Reset file input so the same file can be selected again
      if (fileInputRef.current) {
        fileInputRef.current.value = null;
      }
    }
  };

  const handleRemoveImage = (indexToRemove) => {
    setUploadedImages(prev => prev.filter((_, index) => index !== indexToRemove));
  };

  const isLiveAgentAvailable = () => {
    if (!config?.talk_to_live_agent) {
      return false;
    }

    if (config?.live_agent_always_active) {
      return true;
    }

    const schedule = config?.live_agent_list_of_schedules || [];
    
    try {
      // Get the configured timezone or default to UTC
      const timezone = config?.human_handover_timezone || Intl?.DateTimeFormat()?.resolvedOptions()?.timeZone || 'Europe/Belgrade';
      
      // Get current date/time in the configured timezone using Luxon
      const now = DateTime.now().setZone(timezone);
      const currentDay = now.weekdayLong; // Returns full weekday name
      const currentTime = now.toFormat('HH:mm'); // 24-hour format

      const todaySchedule = schedule.find((s) => s.day === currentDay);

      if (todaySchedule && dynamicButtons?.show_live_agent) {

        // Convert schedule times to DateTime objects in the configured timezone for comparison
        const scheduleStart = DateTime.fromFormat(todaySchedule.from, 'HH:mm', { zone: timezone });
        const scheduleEnd = DateTime.fromFormat(todaySchedule.to, 'HH:mm', { zone: timezone });
        const currentDateTime = DateTime.fromFormat(currentTime, 'HH:mm', { zone: timezone });

        return currentDateTime >= scheduleStart && currentDateTime <= scheduleEnd;
      }
    } catch (error) {
      console.error('Error checking live agent availability:', error);
    }

    return false;
  };

  useEffect(() => {
    setShowConversation(!hiddenConversation);
  }, [hiddenConversation]);

  useEffect(() => {
    setIsMicrophoneOpen(hasMic);
  }, [hasMic]);

  useEffect(() => {
    setIsMicrophoneOpen(false);
  }, [config?.show_microphone]);

  const handleQuickResponseClick = (quickResponseContent) => {
    setQuestion(quickResponseContent);
    setQuestionIsQuickResponse(true);
  };

  console.log(config?.kb_id);
  return (
    <div className="flex-1 min-h-0 h-full relative">
      <div
        className={clsx(
          showConversation &&
            'absolute h-full w-full flex flex-col overflow-hidden'
        )}
      >
        <div className={showConversation ? 'flex h-full' : 'flex'}>
          <div
            className={clsx(
              'flex flex-col grow relative',
              'transition',
              'w-full',
              COMMON_CLASSNAMES.transition.duration.long,
              'data-[enter]:data-[enter-active]:opacity-0',
              'data-[enter]:delay-200',
              {
                'h-full rounded-size1 gap-size2': showConversation,
                'gap-size1': !showConversation,
                'px-size4 py-size5 ':
                  !isPreviewMode && showInAppHeader && showConversation,
              },
              {'md:max-w-[700px] md:mx-auto': isInHumanHandoverApp}
            )}
            style={{
              color: showConversation
                ? 'var(--dt-color-element-100)'
                : undefined,
            }}
          >
            {showInAppHeader && isInApp && (
              <header className="flex gap-size0 items-center justify-between">
                <div
                  className={`flex items-center gap-size0 ${
                    showConversation ? '' : ''
                  }`}
                >
                  {config?.chatbot_icon && !isDanteFaq ? (
                    <img
                      src={config?.chatbot_icon}
                      alt={config?.name || config?.kb_name}
                      className={`${showConversation ? 'size-8' : 'size-5'}`}
                    />
                  ) : (
                    <DShapeLogo
                      className={`${showConversation ? 'size-8' : 'size-5'}`}
                    />
                  )}
                  <span
                    className={`${
                      showConversation ? 'text-xl' : 'text-base'
                    }  tracking-tight`}
                  >
                    {isDanteFaq ? 'Dante FAQ' : config?.name || config?.kb_name}
                  </span>
                </div>
                {showConversation && (
                  <div className="flex items-center gap-size0">
                    {showMenuBtn && !isInApp && (
                      <button className="dbutton border border-grey-5 rounded-size1 p-size1">
                        <OptionsIcon />
                      </button>
                    )}
                    {!isDanteFaq && (
                      <button
                        className="dbutton border border-grey-5 rounded-size1 p-size1"
                        onClick={handleOpenDanteConversations}
                      >
                        <ConversationsIcon className="w-[18px] h-[18px]" />
                      </button>
                    )}
                    {showCloseBtn && (
                      <button
                        className="dbutton bg-grey-2 rounded-size1 p-size1"
                        onClick={handleCloseButton}
                      >
                        <CloseIcon className="w-[18px] h-[18px]" />
                      </button>
                    )}
                  </div>
                )}
              </header>
            )}
            <Transition
              show={config?.notification?.show}
              enter="transition-all duration-300"
              enterFrom="-translate-x-full opacity-0" 
              enterTo="translate-x-0 opacity-100"
              leave="transition-all duration-300"
              leaveFrom="translate-x-0 opacity-100"
              leaveTo="-translate-x-full opacity-0"
            >
              <div className="absolute top-0 left-0 right-0 z-50 bg-white">
                <div
                  className={`transition-all duration-300 data-[closed]:opacity-0 notification-${config?.notification?.type} rounded-size0 w-full`}
                  style={{
                    backgroundColor: 'var(--dt-color-surface-100)',
                  }}
                >
                  <div
                    className={'py-size1 px-size2 flex items-center justify-center text-medium text-sm w-full'}
                    style={{
                      backgroundColor: `var(--dt-color-${config?.notification?.type}-5)`,
                      color: `var(--dt-color-${config?.notification?.type}-100)`,
                    }}
                  >
                    {config?.notification?.message}
                  </div>
                </div>
              </div>
            </Transition>

            {config?.disclaimer_enabled && (!isInApp || isPreviewMode) && (
              <div
                className={'disclaimer flex flex-col gap-size1 items-center text-center'}
              >
                <img
                  src={
                    config?.chatbot_icon instanceof File
                      ? URL.createObjectURL(config?.chatbot_icon)
                      : config?.chatbot_icon || DANTE_THEME_CHAT.chatbot_icon
                  }
                  alt={config?.kb_name}
                  className="size-12"
                />
                <p
                  style={{ color: 'var(--dt-color-element-100)' }}
                  className="text-xl"
                >
                  {config?.kb_name}
                </p>
                <span
                  style={{ color: 'var(--dt-color-element-50)' }}
                  className="block text-sm"
                  dangerouslySetInnerHTML={{ __html: config?.disclaimer_text }}
                ></span>
              </div>
            )}
            {showConversation && (
              <Transition show={showConversation} appear={showConversation}>
                <div
                  className={clsx(
                    'conversation transition duration-300 flex flex-col justify-between',
                    'animate-fadeIn',
                    {
                      'grow h-[1px] opacity-100 gap-size5': showConversation,
                      'opacity-0': !showConversation,
                    },
                  )}
                >
                  {messagesLoading ? (
                    <div className="flex-1 flex flex-col items-center justify-center">
                      <DLoading show={true} style={{ height: '100%', width: '100%' }}/>
                    </div>
                  ) : (
                    <ChatListMessages
                      chatContainerRef={chatContainerRef}
                      messages={config?.messages || []}
                      transformLinkUri={transformLinkUri}
                      chatbot_profile_pic={config?.chatbot_icon}
                      handleFooterButton={handleFooterButton}
                      isDanteFaq={isDanteFaq}
                      isInApp={isInApp}
                      hideFooter={hideFooter}
                      sources={sources}
                      openSources={openSources}
                      setOpenSources={setOpenSources}
                      showSources={showSources}
                      sourcesLoading={sourcesLoading}
                      chatImageLoader={chatImageLoader}
                      isInHumanHandoverApp={isInHumanHandoverApp}
                      interactingWithLiveAgent={interactingWithLiveAgent}
                      openedConversation={openedConversation}
                      pollResponse={pollResponse}
                      showDate={isInHumanHandoverApp}
                      kb_id={config?.kb_id}
                    />
                  )}

                  {((dynamicButtons?.show_calendly_url &&
                    dynamicButtons?.show_live_agent) ||
                    isLiveAgentAvailable() ||
                    (config?.suggested_prompts_enabled &&
                      config?.prompt_suggestions?.length > 0 &&
                      showSuggestionPrompts)) &&
                    showButtons && !isInHumanHandoverApp && (
                      <div className="flex gap-size1 grow-0 overflow-x-auto flex-nowrap h-[38px] justify-start flex-shrink-0 no-scrollbar overflow-y-hidden suggested-prompts-container animate-fadeInUp">
                        {dynamicButtons?.show_calendly_url && !isInApp && (
                          <DSuggestionPrompt
                            type="book_meet"
                            disabled={isPreviewMode}
                            onClick={() =>
                              window.open(config?.calendly_url, '_blank')
                            }
                            content={
                              config?.calendly_btn_text || 'Book a meeting'
                            }
                          />
                        )}
                        {(dynamicButtons?.show_live_agent &&
                          isLiveAgentAvailable()) && !isPreviousConversationHasLiveAgent && !isInApp && !isAnswerLoading && (
                          <DSuggestionPrompt
                            type="connect_live_agent"
                            content={'Connect to Live Agent'}
                            onClick={handleOpenLiveAgent}
                            disabled={isPreviewMode}
                          />
                        )}
                          {config?.suggested_prompts_enabled &&
                            config?.prompt_suggestions?.length > 0 &&
                            showSuggestionPrompts &&
                            config?.prompt_suggestions.map((suggestion, index) => (
                                <Transition
                                  key={index}
                                  show={showSuggestionPrompts}
                                >
                                  <div className="flex-shrink-0">
                                    {showSuggestionPrompts && (
                                      <div
                                        className={clsx(
                                          'suggestion-prompt',
                                          'h-full',
                                          `transition-all duration-200 data-[enter]:delay-${
                                            (index + 1) * 200
                                          } data-[closed]:opacity-0 data-[closed]:translate-y-2`,
                                          `data-[leave]:delay-${
                                            (config?.prompt_suggestions
                                              ?.length -
                                              index) *
                                            200
                                          } data-[leave]:opacity-0`
                                        )}
                                      >
                                        <DSuggestionPrompt
                                          content={suggestion.content}
                                          onClick={() =>
                                            handleSuggestionClick(suggestion)
                                          }
                                          disabled={isPreviewMode || isAnswerLoading}
                                        />
                                      </div>
                                    )}
                                  </div>
                                </Transition>
                            )
                          )}
                      </div>
                    )}

                  {
                    isInHumanHandoverApp && !isLiveAgentResolved && humanHandoverConfig?.isTaken && (
                      <div className="flex gap-size1 grow-0 overflow-x-auto flex-nowrap h-[38px] justify-end px-size1 flex-shrink-0 no-scrollbar overflow-y-hidden">
                        <DButton variant="resolved" onClick={() => humanHandoverMarkResolved()}>
                          <ConfirmIcon />
                          Mark as resolved
                        </DButton>
                      </div>
                    )
                  }
                </div>
              </Transition>
            )}
            <div className={clsx(isInHumanHandoverApp ? 'mx-size1' : '')}>
              {isLiveAgentResolved && isInHumanHandoverApp ? (
                <div className="grow-0 flex justify-center gap-size1 items-center py-size2 text-green-500 whitespace-nowrap">
                  <hr className="w-full border-t border-green-10" />
                  <div className="flex items-center gap-size1">
                    <MarkAsResovledIcon />
                    Agent resolved this
                  </div>
                  <hr className="w-full border-t border-green-10" />
                </div>
              ) : ((!showConsent && !isInHumanHandoverApp) ||
                (isInHumanHandoverApp && humanHandoverConfig?.isTaken)) && (
                <div className="grow-0 flex flex-col gap-size1">
                  {(isInHumanHandoverApp || pollResponse) && uploadedImages.length > 0 && (
                    <div className="flex gap-size1 overflow-x-auto py-size1">
                      {uploadedImages.map((image, index) => (
                        <UploadedImagePreview
                          key={index}
                          image={image}
                          onRemove={() => handleRemoveImage(index)}
                        />
                      ))}
                    </div>
                  )}
                  <div className={clsx('bubble-input-container relative rounded-size0 w-full flex flex-col gap-size1 bg-[--dt-color-surface-100] animate-fadeInUpDelayed3 h-[42px] w-[calc(100%-8px)] mx-auto my-[4px] px-[2px]',
                    // {"bubble-input-container relative rounded-size0 w-full border border-[--dt-color-element-10] flex flex-col gap-size1 pl-size2 bg-[--dt-color-surface-100] animate-fadeInUpDelayed3 grid py-size1 [&>textarea]:text-inherit after:text-inherit [&>textarea]:resize-none [&>textarea]:overflow-hidden [&>textarea]:[grid-area:1/1/2/2] after:[grid-area:1/1/2/2] after:whitespace-pre-wrap after:invisible   after:content-[attr(data-cloned-val)_'_'] after:border after:max-h-16 pr-[64px]": (config?.kb_id !== 'ca7ac107-9716-4ef3-a6b5-55ceab49f15a' && config?.kb_id !== '86927718-7690-4c0c-a99d-8bc8afda0a4c') }
                  )}>
                    {(!isMicrophoneOpen ||
                      !config?.show_microphone ||
                      isRecording) && (
                      <textarea
                        ref={textareaRef}
                        className={clsx(
                          'rounded-full px-3 py-2 transition-colors duration-200 text-min-safe-input bg-transparent no-scrollbar ',
                          (isAnswerLoading || isPreviewMode) ? '!bg-border-[rgba(9, 8, 31,0.05)] border-[rgba(9, 8, 31,0.5)] border-0 cursor-not-allowed' : 'bg-trasnparent text-grey-75 border border-[rgba(9, 8, 31,0.1)] focus:border-purple-300 focus:ring-[0.5px] focus:ring-purple-300 focus:outline-none',
                          {'!p-0': isMicrophoneOpen}
                        )}
                        rows={1}
                        placeholder={
                          config?.input_placeholder_text ||
                          DANTE_THEME_CHAT.input_placeholder_text
                        }
                        style={{
                          resize: 'none',
                          overflowY: 'auto',
                          scrollbarWidth: 'none', /* Firefox */
                          msOverflowStyle: 'none', /* IE and Edge */
                          '&::-webkit-scrollbar': {
                            display: 'none' /* Chrome, Safari and Opera */
                          }
                        }}
                        value={question}
                        disabled={isPreviewMode || isAnswerLoading}
                        onChange={(e) => {
                          setQuestion(e.target.value);
                        }}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') {
                            if (e.shiftKey) {
                              // Allow shift+enter to create a new line
                              return;
                            }
                            if (!isPreviewMode && !isAnswerLoading) {
                              e.preventDefault();
                              handleSend();
                            }
                          }
                        }}
                      />
                    )}
                    {isMicrophoneOpen && config?.show_microphone && (
                      <AudioRecorder
                        handleRecordingComplete={handleRecordingComplete}
                        setQuestion={setQuestion}
                        isRecording={isRecording}
                        setIsRecording={setIsRecording}
                        isPreviewMode={isPreviewMode}
                      />
                    )}

                    <div className="absolute bottom-0 top-0 my-auto right-[6px] flex items-center">
                      {isInHumanHandoverApp && (
                        <MenuQuickResponse
                          quickResponses={humanHandoverConfig?.quick_responses}
                          handleQuickResponseClick={handleQuickResponseClick}
                        />
                      )}

                      {(isInHumanHandoverApp || pollResponse) && (
                        <input
                          type="file"
                          accept="image/*"
                          multiple
                          className="hidden"
                          ref={fileInputRef}
                          onChange={handleImageUpload}
                        />
                      )}

                      {(isInHumanHandoverApp || pollResponse) && (
                        <DButtonIcon
                          className="p-size1 size-8 rounded-size0 flex items-end justify-center"
                          onClick={() => fileInputRef.current?.click()}
                        >
                          <AttachmentIcon className="w-8 h-8" />
                        </DButtonIcon>
                      )}

                      {config?.realtime_voice_access && !isInHumanHandoverApp && !pollResponse && (
                        <div className="relative group">
                          <DButtonIcon
                            onClick={handleShowVoice}
                            className="animate-pulse hover:animate-none"
                          >
                            <AiVoiceIcon className="text-blue-500 group-hover:text-blue-600 transition-colors duration-300" />
                          </DButtonIcon>
                          {!isInApp && config?.show_voice_tooltip && <div className="absolute bottom-full -right-[30px] px-3 py-2 bg-purple-200/50 text-white text-sm rounded-lg group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap shadow-[0_0_15px_rgba(168,85,247,0.5)] min-w-[105px]" id="voice-tooltip">
                              <span>
                                {config?.voice_tooltip_text 
                                  ? config.voice_tooltip_text 
                                  : 'Click here to start talking to me!'}
                              </span>
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  document.getElementById('voice-tooltip').style.display = 'none';
                                }}
                                className="hover:text-purple-200 transition-colors absolute top-[2px] right-[2px]"
                              >
                                <CloseIcon className="w-[12px] h-[12px] text-white/80" />
                              </button>
                            <div className="absolute right-[40px] -bottom-[5px] w-0 h-0 border-l-[6px] border-l-transparent border-t-[6px] border-t-purple-50 border-r-[6px] border-r-transparent"></div>
                          </div>}
                        </div>
                      )}

                      {config?.show_microphone && !isRecording && (
                        <Menu>
                          <MenuButton className="p-size1 size-8 rounded-full flex items-end justify-center bg-black">
                            <ChevronDownIcon className="w-[14px] h-[14px] text-white" />
                          </MenuButton>
                          <MenuItems
                            transition
                            className="transition duration-150 ease-out data-[closed]:scale-95 data-[closed]:opacity-0 data-[closed]:translate-y-2  bg-black rounded-size0 flex flex-col gap-size1 justify-center items-center py-size0 w-[32px] translate-y-[-2px]"
                            anchor="top start"
                          >
                            <MenuItem>
                              <button
                                className="dbutton send-message-button flex items-center justify-center hover:bg-[#FFFFFF26] p-size0 rounded-size0"
                                onClick={(e) => {
                                  e.preventDefault();
                                  handleSend(question);
                                }}
                              >
                                <SendIcon className="text-white w-[14px] h-[14px]" />
                              </button>
                            </MenuItem>
                            {config?.show_microphone && !isMicrophoneOpen ? (
                              <MenuItem>
                                <button
                                  className="dbutton open-microphone-button flex items-center justify-center hover:bg-[#FFFFFF26] p-size0 rounded-size0"
                                  onClick={handleOpenMicrophone}
                                >
                                  <MicrophoneIcon className="text-white w-[13px] h-[13px]" />
                                </button>
                              </MenuItem>
                            ) : (
                              <MenuItem>
                                <button
                                  className="dbutton open-chatbot-button flex items-center justify-center hover:bg-[#FFFFFF26] p-size0 rounded-size0"
                                  onClick={() => {
                                    setIsMicrophoneOpen(false);
                                    setIsRecording(false);
                                  }}
                                >
                                  <AiChatbotIcon className="text-white w-[13px] h-[13px]" />
                                </button>
                              </MenuItem>
                            )}
                            {/* {config?.realtime_voice_access && (
                              <MenuItem>
                                <button className="dbutton flex items-center justify-center hover:bg-[#FFFFFF26] p-size0 rounded-size0">
                                  <AiVoiceIcon className="text-white w-[14px] h-[14px]" />
                                </button>
                              </MenuItem>
                            )} */}
                          </MenuItems>
                        </Menu>
                      )}
                      {!config?.show_microphone &&
                        // !config?.realtime_voice_access &&
                        !isRecording && (
                          <DButtonIcon
                            className={clsx(
                              '!rounded-full',
                              'send-message-button',
                              'text-[rgba(9, 8, 31,0.5)]',
                              isAnswerLoading || isPreviewMode ? '!bg-[rgba(9, 8, 31,0.05)]' : ''
                            )}
                            onClick={() => handleSend()}
                            style={{
                              color: 'var(--dt-color-element-100)',
                              backgroundColor: 'var(--dt-color-surface-100)',
                            }}
                            disabled={isAnswerLoading || isPreviewMode}
                          >
                            <SendIcon className={`w-4 h-4 text-[rgba(9, 8, 31,0.5)]`} />
                          </DButtonIcon>
                        )}
                    </div>
                  </div>
                  {!isPreviewMode &&
                    (isInApp || isDanteFaq) && !isInHumanHandoverApp && (
                      <div
                        className={clsx(
                          'flex items-center justify-between',
                          isDanteFaq ? 'self-end' : ''
                        )}
                      >
                        {!isDanteFaq &&(
                          <div>
                            <LLMSelector />
                          </div>
                        )}

                        {/* <div className="flex items-center gap-size1 text-xs self-end">
                          Submit <ReturnIcon width={12} height={12} />
                        </div> */}
                      </div>
                    )}
                  {!isInApp &&
                   (!hiddenPoweredByDante && !config?.remove_watermark) && (
                      <div className="flex items-center text-xs justify-between">
                        <PoweredByDante
                          surface={config?.surface_color}
                          isPreviewMode={isPreviewMode}
                        />
                      </div>
                    )}
                </div>
              )}
              {showConsent && !isInHumanHandoverApp && (
                <div className="grow-0 flex gap-size1 w-full">
                  <DButton
                    variant="dark"
                    fullWidth
                    onClick={() => {
                      setQuestion('yes');
                      setShowConsent(false);
                      handleSend('yes');
                    }}
                  >
                    Yes
                  </DButton>
                  <DButton
                    variant="outlined"
                    fullWidth
                    onClick={() => {
                      setQuestion('no');
                      setShowConsent(false);
                      handleSend('no');
                    }}
                  >
                    No
                  </DButton>
                </div>
              )}
              {isInHumanHandoverApp && !humanHandoverConfig?.isTaken && (
                <div className="grow-0 flex gap-size1 w-full mb-size2">
                  <DButton
                    variant="grey"
                    fullWidth
                    size="md"
                    onClick={() => {
                      humanHandoverMarkResolved();
                    }}
                  >
                    Mark as resolved
                  </DButton>
                  <DButton
                    variant="dark"
                    fullWidth
                    size="md"
                    onClick={() => {
                      humanHandoverTakeConversation();
                    }}
                  >
                    Join
                  </DButton>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
      <div className="relative">
        <DModalEmailTransaction
          open={showEmailTranscript}
          onClose={() => setShowEmailTranscript(false)}
          conversationId={currectConversationId}
          token={config?.token}
          setNotification={setNotification}
        />
      </div>
    </div>
  );
};

export default Chat;
