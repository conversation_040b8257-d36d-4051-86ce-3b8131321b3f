import React from 'react';
import { isChunkLoadingError, reloadOnChunkError } from '@/utils/chunkLoadingErrorHandler';
import DLoading from '@/components/DLoading';

/**
 * Error Boundary that specifically handles chunk loading failures
 *
 * When a chunk loading error occurs, it automatically reloads the page
 * to get fresh assets instead of showing an error page to the user.
 */
class ChunkLoadErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, isReloading: false };
  }

  static getDerivedStateFromError(error) {
    // Check if this is a chunk loading error
    if (isChunkLoadingError(error)) {
      return { hasError: true, isReloading: true };
    }

    // For other errors, let them bubble up to other error boundaries
    throw error;
  }

  componentDidCatch(error, errorInfo) {
    // Handle chunk loading errors
    if (isChunkLoadingError(error)) {
      console.warn('ChunkLoadErrorBoundary caught chunk loading error:', error.message);

      // Attempt to reload the page
      if (reloadOnChunkError(error)) {
        return; // Page will reload, no need to render error state
      }
    }

    // For non-chunk errors, re-throw to let other error boundaries handle them
    throw error;
  }

  render() {
    if (this.state.hasError && this.state.isReloading) {
      // Show the standard app loading screen - users won't notice the error
      return <DLoading show={true} />;
    }

    return this.props.children;
  }
}

export default ChunkLoadErrorBoundary;
