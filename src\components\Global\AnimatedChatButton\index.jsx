import React from 'react';
import AiChatbotIcon from '../Icons/AiChatbotIcon';

const AnimatedChatButton = ({ onClick, isOpen }) => {
  return (
    <div className={`flex items-center justify-center overflow-hidden transition-all duration-300 ease-in-out ${isOpen ? 'w-0 opacity-0' : 'w-auto opacity-100'}`}>
      <div className="relative flex items-center justify-center">
        <button
          onClick={onClick}
          className="relative bg-white rounded-size0 flex items-center px-4 py-1 min-h-[32px] transition-all duration-200"
        >
          <AiChatbotIcon className="w-4 h-4 text-purple-300 mr-2" />
          <span className="font-medium text-black text-base whitespace-nowrap">Chat with us</span>
        </button>
      </div>
    </div>
  );
};

export default AnimatedChatButton; 