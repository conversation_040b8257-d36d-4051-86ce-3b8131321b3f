import DModal from '../Global/DModal';
import DTooltip from '../Global/DTooltip';
import FileIcon from '../Global/Icons/FileIcon';
import InfoIcon from '../Global/Icons/InfoIcon';
import LinkIcon from '../Global/Icons/LinkIcon';

const SourcesPopup = ({ open, onClose, data = [] }) => {
  return (
    <DModal
        isOpen={open}
        onClose={onClose}
        title="Sources"
    >
        <div className="flex flex-col gap-size2 max-h-[500px] overflow-y-auto">
            {data && data.length > 0 ? data.map((source, index) => (
                <div key={index} className="flex gap-size1">
                    <div className="size-10 rounded-full border border-[var(--dt-color-element-2)] flex items-center justify-center shrink-0">
                        {source.file_name.startsWith('http') 
                            ? <LinkIcon className="size-5 text-[var(--dt-color-element-50)]" />
                            : <FileIcon className="size-4 text-[var(--dt-color-element-50)]" />}
                    </div>
                    <div className="flex flex-col">
                        <div className="flex items-center gap-size1">
                            {source.file_name.startsWith('http') 
                            ? <a href={source.file_name} target="_blank" rel="noopener noreferrer" className="text-black">
                                <p className="text-sm font-regular">{source.file_name}</p></a> 
                            : <p className="text-sm font-regular">{source.file_name}</p>}
                            <DTooltip
                                content={source.text}
                                placement="right"
                            >
                                <InfoIcon className="size-3 text-[var(--dt-color-element-50)]" />
                            </DTooltip>
                        </div>
                        <p className="text-xs text-[var(--dt-color-element-50)]">Page: {source.page}</p>
                    </div>

                </div>
            )) : <p className="text-sm font-regular text-center">No sources found for this answer</p>}
        </div>
    </DModal>
  )
}

export default SourcesPopup;