import convertCustomCss from '@/helpers/convertCustomCss';
import convertThemeToCSSVariablesStyle from '@/helpers/convertThemeToCSSVariables';

const StyleTag = ({ tag, tempCustomizationData, customImport }) => {
  return <style>{`
    ${customImport ? `@import url(${customImport});` : ''}
    ${tag} {
        font-family: var(--dt-font-family), -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
        font-size: var(--dt-font-size) !important;
        ${convertThemeToCSSVariablesStyle({
        color: {
            alert: tempCustomizationData.alert_color,
            positive: tempCustomizationData.positive_color,
            negative: tempCustomizationData.negative_color,
            surface: tempCustomizationData.surface_color,
            element: tempCustomizationData.element_color,
            brand: tempCustomizationData.brand_color,
        },
        font: {
            family: tempCustomizationData.font_name,
            size: tempCustomizationData.font_size,
        },
        })}
    }
    ${tempCustomizationData.custom_css !== '' ? convertCustomCss(tempCustomizationData) : ''}`}
    </style>;
};

export default StyleTag;
