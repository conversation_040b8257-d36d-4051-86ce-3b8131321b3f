import React, { useEffect } from 'react';
import DLoading from '@/components/DLoading';

/**
 * Simple loading screen that shows when chunk loading fails
 * Uses the app's standard loading component so users don't notice the error
 * Automatically reloads the page after a short delay
 */
const UpdateScreen = ({ onReload = () => window.location.reload() }) => {
  useEffect(() => {
    // Auto-reload after 1 second - fast enough to be seamless
    const timer = setTimeout(() => {
      onReload();
    }, 1000);

    return () => clearTimeout(timer);
  }, [onReload]);

  // Just show the standard app loading screen
  return <DLoading show={true} />;
};

export default UpdateScreen;
