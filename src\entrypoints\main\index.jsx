import React from 'react';
import ReactDOM from 'react-dom/client';
import { <PERSON><PERSON>etProvider } from 'react-helmet-async';

import { GoogleOAuthProvider } from '@react-oauth/google';
import App from './App';
import * as amplitude from '@amplitude/analytics-browser';
import './index.css';
import userflow from 'userflow.js'
import { setupChunkLoadingErrorHandler } from '@/utils/chunkLoadingErrorHandler';
import ChunkLoadErrorBoundary from '@/components/ChunkLoadErrorBoundary';

const root = ReactDOM.createRoot(document.getElementById('root'));
const helmetContext = {};

const params = {};
const queryString = window.location.search.substring(1);
const pairs = queryString.split('&');

pairs.forEach((pair) => {
  const [key, value] = pair.split('=');
  if (key) {
    params[decodeURIComponent(key)] = decodeURIComponent(value || '');
  }
});

userflow.init('*****************************') // Your token for Production

amplitude.init(import.meta.env.VITE_APP_AMPLITUDE_API_KEY, {
  deviceId: params.deviceId || undefined,
});

// Setup global chunk loading error handler
setupChunkLoadingErrorHandler();

root.render(
  // <React.StrictMode>
  <ChunkLoadErrorBoundary>
    <GoogleOAuthProvider clientId={import.meta.env.VITE_APP_GOOGLE_CLIENT_ID}>
      <HelmetProvider context={helmetContext}>
        <App />
      </HelmetProvider>
    </GoogleOAuthProvider>
  </ChunkLoadErrorBoundary>
  // </React.StrictMode>
);
