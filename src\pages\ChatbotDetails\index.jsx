import { useEffect, useState, useRef } from 'react';
import { useParams, useLocation } from 'react-router-dom';
import ReactRouterPrompt from 'react-router-prompt';

import ChatbotShortcuts from '@/components/Chatbot/ChatbotShortcuts';
import Conversations from '@/components/Conversations';
import DLoading from '@/components/DLoading';
import DConfirmationModal from '@/components/Global/DConfirmationModal';
import useDante<PERSON><PERSON> from '@/hooks/useDanteApi';
import * as chatbotService from '@/services/chatbot.service';
import { useChatbotStore } from '@/stores/chatbot/chatbotStore';
import useLayoutStore from '@/stores/layout/layoutStore';
import { useUserStore } from '@/stores/user/userStore';
import { useCustomizationStore } from '@/stores/customization/customizationStore';
import useIsAboveBreakpoint from '@/helpers/useIsAboveBreakpoint';
import { Transition } from '@headlessui/react';
import clsx from 'clsx';
import * as customizationService from '@/services/customization.service';
import CompletionPopup from '@/components/Chatbot/Create/CompletionPopup';
import Bubble from '@/components/Bubble';
import VoicePreview from '@/components/Voice/VoicePreview';
import VoiceAgentIcon from '@/components/Global/Icons/VoiceAgentIcon';
import ConversationsIcon from '@/components/Global/Icons/ConversationsIcon';
import StyleTag from '@/components/StyleTag';

const ChatbotDetails = () => {
  let params = useParams();
  const location = useLocation();
  const isAboveMd = useIsAboveBreakpoint('md');
  const setLayoutTitle = useLayoutStore((state) => state.setLayoutTitle);
  const setSidebarOpen = useLayoutStore((state) => state.setSidebarOpen);
  const setSelectedChatbot = useChatbotStore(
    (state) => state.setSelectedChatbot
  );
  const selectedChatbot = useChatbotStore((state) => state.selectedChatbot);
  const setProgressBar = useLayoutStore((state) => state.setProgressBar);
  const setChatbotCustomization = useCustomizationStore(
    (state) => state.setChatbotCustomization
  );
  const { auth } = useUserStore((state) => state);

  const [customizationData, setCustomizationData] = useState(false);
  const [shouldRefetchConversations, setShouldRefetchConversations] = useState(false);
  const [isVoicePreviewPlaying, setIsVoicePreviewPlaying] = useState(false);
  const [showCompletionPopup, setShowCompletionPopup] = useState(false);
  const [showVoicePreview, setShowVoicePreview] = useState(false);
  const [showConversations, setShowConversations] = useState(false);
  const [isVoiceButtonHovered, setIsVoiceButtonHovered] = useState(false);
  const [isConversationsButtonHovered, setIsConversationsButtonHovered] = useState(false);
  const isAboveSm = useIsAboveBreakpoint('sm');

  const voiceButtonRef = useRef(null);
  const conversationsButtonRef = useRef(null);

  const { data, isLoading } = useDanteApi(
    chatbotService.getChatbotInfoById,
    [],
    {},
    params.id
  );

  const {
    data: dataOverview,
    loading: loadingOverview,
    refetch: refetchOverview,
  } = useDanteApi(customizationService.getChatbotOverviewById, [], {}, params.id);

  const { data: customizationRawData, isLoading: isLoadingCustomization } =
    useDanteApi(customizationService.getChatbotCustomizationById, [], {}, {
      kb_id: params.id,
    });

  const forceRefetchConversations = () => {
    setShouldRefetchConversations(!shouldRefetchConversations);
  };

  useEffect(() => {
    setSidebarOpen(false);
    if (!isAboveMd) {
      setLayoutTitle('Chatbot');
    } else {
      setLayoutTitle('');
    }
  }, [window.innerWidth]);

  useEffect(() => {
    setSelectedChatbot(data?.results);
  }, [data, setSelectedChatbot]);

  useEffect(() => {
    setProgressBar([]);
  }, []);

  // Check if we should show the completion popup
  useEffect(() => {
    // Check if we just came from chatbot creation
    const fromChatbotCreation = location.state?.fromChatbotCreation;

    if (fromChatbotCreation && data?.results) {
      setShowCompletionPopup(true);
      // Clear the state so the popup doesn't show again on refresh
      window.history.replaceState({}, document.title, location.pathname);
    }
  }, [location, data]);

  useEffect(() => {
    if (customizationRawData) {
      setCustomizationData({
        initial_messages: customizationRawData?.initial_messages,
        prompt_suggestions: customizationRawData?.prompt_suggestions,
        suggested_prompts_enabled:
          customizationRawData?.suggested_prompts_enabled,
        suggested_prompts_appearance:
          customizationRawData?.suggested_prompts_appearance,
        ...customizationRawData,
      });
    }
    setChatbotCustomization(customizationRawData);
  }, [customizationRawData]);

  // Create a function to handle the voice preview state changes
  const handleVoicePreviewStateChange = (isPlaying) => {
    setIsVoicePreviewPlaying(isPlaying);
  };

  if (isLoading) {
    return <DLoading show={isLoading} />;
  }

  // Configuration for the Bubble component
  const bubbleConfig = {
    kb_id: params.id,
    access_token: auth?.access_token,
    chatbot_profile_pic: dataOverview?.icon,
    ...customizationData,
    name: dataOverview?.name,
    public: true,
    home_tab_enabled: true,
    initialActiveTab: 'chat'
  };

  const toggleVoicePreview = () => {
    setShowVoicePreview(!showVoicePreview);
    if (!showVoicePreview) {
      setShowConversations(false);
    }
  };

  const toggleConversations = () => {
    setShowConversations(!showConversations);
    if (!showConversations) {
      setShowVoicePreview(false);
    }
  };

  return (
    <div className="flex flex-col w-full relative h-full">
      <div className="flex flex-col min-h-screen md:min-h-0 md:h-full w-full">
        <div className="flex flex-col h-full min-h-[80vh] md:h-full relative overflow-hidden p-3">
          {/* Grid Background Pattern */}
          <div 
            className="absolute top-0 left-0 w-full h-full -z-0 bg-grey-2"
            style={{
              backgroundImage: `
                linear-gradient(rgba(100, 100, 100, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(100, 100, 100, 0.1) 1px, transparent 1px)
              `,
              backgroundSize: '20px 20px',
              backgroundColor: 'bg-white'
            }}
          />
          
          <div className="flex flex-col z-20 absolute top-2 right-2">
            {/* Top Buttons Container */}
            <div className="flex justify-end items-center gap-4 mb-4">
              {/* Voice Preview Button */}
              <button
                ref={voiceButtonRef}
                className={`group bg-purple-300 hover:bg-purple-400 text-white rounded-full p-3 shadow-lg transition-all duration-500 ease-in-out flex items-center overflow-hidden ${
                  isVoiceButtonHovered ? 'w-auto' : 'w-12 justify-center'
                }`}
                onClick={toggleVoicePreview}
                onMouseEnter={() => setIsVoiceButtonHovered(true)}
                onMouseLeave={() => setIsVoiceButtonHovered(false)}
                title={showVoicePreview ? "Hide Voice Agent" : "Show Voice Agent"}
              >
                <VoiceAgentIcon className="size-5 flex-shrink-0 text-[#fff]" />
                {isVoiceButtonHovered && (
                  <span className="text-sm whitespace-nowrap ml-2 text-white">
                    Turn your chatbot into an AI voice agent
                  </span>
                )}
              </button>

              {/* Conversations Button */}
              <button
                ref={conversationsButtonRef}
                className={`group bg-purple-300 hover:bg-purple-400 text-white rounded-full p-3 shadow-lg transition-all duration-500 ease-in-out flex items-center overflow-hidden ${
                  isConversationsButtonHovered ? 'w-auto' : 'w-12 justify-center'
                }`}
                onClick={toggleConversations}
                onMouseEnter={() => setIsConversationsButtonHovered(true)}
                onMouseLeave={() => setIsConversationsButtonHovered(false)}
                title={showConversations ? "Hide Conversations" : "Show Conversations"}
              >
                <ConversationsIcon className="size-5 flex-shrink-0 text-[#fff]" />
                {isConversationsButtonHovered && (
                  <span className="text-sm whitespace-nowrap ml-2 text-white">
                    View Conversations
                  </span>
                )}
              </button>
            </div>
          </div>

            {/* Main Content Container */}
            <div className="flex flex-1 justify-center items-center gap-size5 max-h-[85vh] mt-4">
              <div className="w-full max-w-[500px] border h-full  border-grey-10 bg-white rounded-size1 shadow-sm overflow-hidden">
              <StyleTag tag=".bubble" tempCustomizationData={customizationData} />
                {customizationData && (
                  <Bubble
                    config={bubbleConfig}
                    type="chatbot"
                    isInApp={true}
                    initialShouldFetchCustomization={false}
                    isPreviewMode={false}
                    hiddenPoweredByDante
                    forceRefetch={forceRefetchConversations}
                    handleOpenDanteConversations={() => {
                      // setShowConversation(true);
                      // handleRightSidebar();
                    }}
                    onNewConversationCreated={(newConversation) => {
                      forceRefetchConversations();
                    }}
                  />
                )}
              </div>

              {/* Slide-in Voice Preview Panel */}
              <div 
                className={`w-[360px] bg-white/50 h-full backdrop-blur-sm transition-opacity duration-300 ease-in-out z-10 shadow-lg ${
                  showVoicePreview ? 'block opacity-100' : 'hidden opacity-0 pointer-events-none'
                }`}
              >
                <div className="w-full h-full flex flex-col">
                  <VoicePreview
                    chatbotId={params.id}
                    voiceId="a56a7938-bd6b-44b4-8e52-2c652946d528"
                    welcomeMessage={`I'm your AI Voice Assistant, fully trained on your chatbot data. Ask me anything connected to your chatbot data, and I'll provide the answers. Let's begin! ${customizationData?.initial_messages?.map(msg => msg.content).join(' ')}`}
                    phoneNumbers={customizationData?.phone_numbers}
                    personalityPrompt={customizationData?.personality_prompt}
                    hideCloseButton={true}
                    place="chatbot-shortcuts"
                  />
                </div>
              </div>

              {/* Slide-in Conversations Panel */}
              <div 
                className={`w-[360px] bg-white/50 h-full backdrop-blur-sm transition-opacity duration-300 ease-in-out z-10 shadow-lg rounded-size1 ${
                  showConversations ? 'block opacity-100' : 'hidden opacity-0 pointer-events-none'
                }`}
              >
                <div className="w-full h-full flex flex-col">
                  <Conversations
                    onClose={(type = 'close') => {
                      if (type === 'openConversation') {
                        !isAboveSm && setShowConversations(false);
                      } else {
                        setShowConversations(false);
                      }
                    }}
                    forceRefetch={forceRefetchConversations}
                    shouldRefetch={shouldRefetchConversations}
                  />
                </div>
              </div>
            </div>
        </div>
      </div>

      {/* Completion Popup */}
      <CompletionPopup
        isOpen={showCompletionPopup}
        onClose={() => setShowCompletionPopup(false)}
        chatbotId={params.id}
      />

      {/* Voice confirmation prompt for navigation */}
      <ReactRouterPrompt when={isVoicePreviewPlaying}>
        {({ isActive, onConfirm, onCancel }) => (
          <DConfirmationModal
            open={isActive}
            onClose={onCancel}
            onConfirm={onConfirm}
            title="Stop AI Voice Agent?"
            description="Leaving this page will stop the AI Voice Agent that is currently speaking. Are you sure you want to leave?"
            confirmText="Leave"
            cancelText="Cancel"
            variantConfirm="danger"
          />
        )}
      </ReactRouterPrompt>
    </div>
  );
};

export default ChatbotDetails;
